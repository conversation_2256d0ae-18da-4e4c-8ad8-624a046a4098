import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const DashboardHome = ({ setPageTitle }) => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  useEffect(() => {
    setPageTitle('لوحة التحكم');
  }, [setPageTitle]);

  // Mock data for demonstration
  const stats = {
    coursesCount: 12,
    studentsCount: 156,
    activeCourses: 8,
    recentEnrollments: 23
  };

  const recentCourses = [
    { id: 1, title: 'دورة البرمجة الأساسية', students: 45, status: 'نشط' },
    { id: 2, title: 'تطوير المواقع', students: 32, status: 'نشط' },
    { id: 3, title: 'قواعد البيانات', students: 28, status: 'مكتمل' }
  ];

  return (
    <div className="dashboard-home">
      <div className="welcome-section">
        <h1>مرحباً بك، {currentUser?.name || 'المدير'}</h1>
        <p>إليك نظرة عامة على منصة علاء عبد الحميد التعليمية</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📚</div>
          <div className="stat-content">
            <h3>{stats.coursesCount}</h3>
            <p>إجمالي الدورات</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{stats.studentsCount}</h3>
            <p>إجمالي الطلاب</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>{stats.activeCourses}</h3>
            <p>الدورات النشطة</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h3>{stats.recentEnrollments}</h3>
            <p>التسجيلات الحديثة</p>
          </div>
        </div>
      </div>

      <div className="recent-courses">
        <h2>الدورات الحديثة</h2>
        <div className="courses-list">
          {recentCourses.map(course => (
            <div key={course.id} className="course-item">
              <div className="course-info">
                <h3>{course.title}</h3>
                <p>{course.students} طالب</p>
              </div>
              <div className="course-status">
                <span className={`status ${course.status === 'نشط' ? 'active' : 'completed'}`}>
                  {course.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="quick-actions">
        <h2>الإجراءات السريعة</h2>
        <div className="actions-grid">
          <button 
            className="action-btn"
            onClick={() => navigate('/admin/courses/new')}
          >
            <span className="action-icon">➕</span>
            إضافة دورة جديدة
          </button>
          
          <button 
            className="action-btn"
            onClick={() => navigate('/admin/students')}
          >
            <span className="action-icon">👥</span>
            إدارة الطلاب
          </button>
          
          <button 
            className="action-btn"
            onClick={() => navigate('/admin/certificates')}
          >
            <span className="action-icon">🏆</span>
            إصدار شهادات
          </button>
          
          <button 
            className="action-btn"
            onClick={() => navigate('/admin/analytics')}
          >
            <span className="action-icon">📊</span>
            عرض التقارير
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardHome;
