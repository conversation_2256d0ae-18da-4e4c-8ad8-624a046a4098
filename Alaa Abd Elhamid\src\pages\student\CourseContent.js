import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { db } from '../../services/firebase';
import { doc, getDoc, collection, getDocs, query, where, orderBy, updateDoc, arrayUnion } from 'firebase/firestore';
import { formatDuration } from '../../utils';

const CourseContent = ({ setPageTitle }) => {
  const { currentUser } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const { courseId, contentId } = useParams();

  const [course, setCourse] = useState(null);
  const [modules, setModules] = useState([]);
  const [currentContent, setCurrentContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [videoProgress, setVideoProgress] = useState(0);

  useEffect(() => {
    setPageTitle('محتوى الدورة');

    const fetchCourseData = async () => {
      try {
        // Fetch course details
        const courseDoc = doc(db, 'courses', courseId);
        const courseSnapshot = await getDoc(courseDoc);

        if (!courseSnapshot.exists()) {
          showNotification('الدورة غير موجودة', 'error');
          navigate('/student/courses');
          return;
        }

        const courseData = courseSnapshot.data();
        setCourse({
          id: courseSnapshot.id,
          ...courseData
        });

        // Fetch course modules
        const modulesCollection = collection(db, 'modules');
        const modulesQuery = query(
          modulesCollection,
          where('courseId', '==', courseId),
          orderBy('order', 'asc')
        );
        const modulesSnapshot = await getDocs(modulesQuery);

        const modulesList = [];
        for (const moduleDoc of modulesSnapshot.docs) {
          const moduleData = moduleDoc.data();

          // Fetch module content
          const contentCollection = collection(db, 'content');
          const contentQuery = query(
            contentCollection,
            where('moduleId', '==', moduleDoc.id),
            orderBy('order', 'asc')
          );
          const contentSnapshot = await getDocs(contentQuery);

          const contentList = contentSnapshot.docs.map(contentDoc => ({
            id: contentDoc.id,
            ...contentDoc.data()
          }));

          modulesList.push({
            id: moduleDoc.id,
            ...moduleData,
            content: contentList
          });
        }

        setModules(modulesList);

        // If contentId is provided, fetch that specific content
        if (contentId) {
          const contentDoc = doc(db, 'content', contentId);
          const contentSnapshot = await getDoc(contentDoc);

          if (contentSnapshot.exists()) {
            const contentData = contentSnapshot.data();
            setCurrentContent({
              id: contentSnapshot.id,
              ...contentData
            });
          }
        } else if (modulesList.length > 0 && modulesList[0].content.length > 0) {
          // If no contentId is provided, show the first content of the first module
          setCurrentContent(modulesList[0].content[0]);
        }
      } catch (error) {
        showNotification('حدث خطأ أثناء جلب بيانات الدورة', 'error');
        console.error('Error fetching course data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [setPageTitle, courseId, contentId, navigate, showNotification]);

  const handleContentClick = (content) => {
    setCurrentContent(content);
    navigate(`/student/courses/${courseId}/content/${content.id}`);
  };

  const handleVideoProgress = async (progress) => {
    setVideoProgress(progress);

    try {
      // Update student progress for this content
      const progressDoc = doc(db, 'progress', `${currentUser.uid}_${currentContent.id}`);

      // Check if progress document exists
      const progressSnapshot = await getDoc(progressDoc);

      if (progressSnapshot.exists()) {
        await updateDoc(progressDoc, {
          progress: progress,
          lastWatched: new Date()
        });
      } else {
        // Create new progress document
        await updateDoc(doc(db, 'users', currentUser.uid), {
          progress: arrayUnion({
            contentId: currentContent.id,
            courseId: courseId,
            progress: progress,
            lastWatched: new Date()
          })
        });
      }

      // Update course progress
      updateCourseProgress();
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const updateCourseProgress = async () => {
    try {
      // Calculate overall course progress
      let totalContents = 0;
      let completedContents = 0;

      modules.forEach(module => {
        totalContents += module.content.length;
        module.content.forEach(content => {
          // In a real app, you would fetch the actual progress for each content
          // For now, we'll assume a content is completed if progress > 90%
          if (content.id === currentContent.id && videoProgress > 90) {
            completedContents++;
          }
        });
      });

      const courseProgress = Math.round((completedContents / totalContents) * 100);

      // Update course progress in Firestore
      await updateDoc(doc(db, 'courses', courseId), {
        [`studentsProgress.${currentUser.uid}`]: courseProgress
      });
    } catch (error) {
      console.error('Error updating course progress:', error);
    }
  };

  const markContentComplete = async () => {
    try {
      // Mark content as completed
      const progressDoc = doc(db, 'progress', `${currentUser.uid}_${currentContent.id}`);

      await updateDoc(progressDoc, {
        progress: 100,
        completed: true,
        completedAt: new Date()
      });

      showNotification('تم إكمال المحتوى بنجاح', 'success');

      // Move to next content if available
      const currentModule = modules.find(module => 
        module.content.some(content => content.id === currentContent.id)
      );

      if (currentModule) {
        const currentContentIndex = currentModule.content.findIndex(content => 
          content.id === currentContent.id
        );

        if (currentContentIndex < currentModule.content.length - 1) {
          // Next content in the same module
          handleContentClick(currentModule.content[currentContentIndex + 1]);
        } else {
          // First content of the next module
          const currentModuleIndex = modules.findIndex(module => module.id === currentModule.id);
          if (currentModuleIndex < modules.length - 1) {
            const nextModule = modules[currentModuleIndex + 1];
            if (nextModule.content.length > 0) {
              handleContentClick(nextModule.content[0]);
            }
          }
        }
      }

      // Update course progress
      updateCourseProgress();
    } catch (error) {
      showNotification('حدث خطأ أثناء تحديث التقدم', 'error');
      console.error('Error marking content as complete:', error);
    }
  };

  if (loading) {
    return <div className="loading-spinner">جاري التحميل...</div>;
  }

  if (!course) {
    return <div className="error-message">لم يتم العثور على الدورة</div>;
  }

  return (
    <div className="course-content-page">
      <div className="course-header">
        <h1>{course.title}</h1>
        <button
          className="btn btn-secondary"
          onClick={() => navigate(`/student/courses/${courseId}`)}
        >
          العودة للدورة
        </button>
      </div>

      <div className="course-content-container">
        <div className="course-sidebar">
          <div className="modules-list">
            <h2>محتويات الدورة</h2>
            {modules.length > 0 ? (
              modules.map(module => (
                <div key={module.id} className="module">
                  <h3>{module.title}</h3>
                  <div className="module-content">
                    {module.content.map(content => (
                      <div
                        key={content.id}
                        className={`content-item ${currentContent?.id === content.id ? 'active' : ''}`}
                        onClick={() => handleContentClick(content)}
                      >
                        <div className="content-icon">
                          {content.type === 'video' ? (
                            <i className="fas fa-play-circle"></i>
                          ) : (
                            <i className="fas fa-file-alt"></i>
                          )}
                        </div>
                        <div className="content-info">
                          <h4>{content.title}</h4>
                          <div className="content-meta">
                            <span className="duration">
                              {content.duration ? formatDuration(content.duration) : ''}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-state">
                <p>لا توجد محتويات متاحة لهذه الدورة</p>
              </div>
            )}
          </div>
        </div>

        <div className="content-main">
          {currentContent ? (
            <>
              <div className="content-header">
                <h2>{currentContent.title}</h2>
                <div className="content-actions">
                  <button
                    className="btn btn-success"
                    onClick={markContentComplete}
                  >
                    إكمال والمتابعة
                  </button>
                </div>
              </div>

              <div className="content-body">
                {currentContent.type === 'video' ? (
                  <div className="video-player">
                    <div className="video-container">
                      {/* In a real app, you would use a video player like React Player */}
                      <div className="video-placeholder">
                        <i className="fas fa-play-circle"></i>
                        <p>مشغل الفيديو</p>
                        <p>{currentContent.title}</p>
                      </div>
                    </div>
                    <div className="video-controls">
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${videoProgress}%` }}
                        ></div>
                      </div>
                      <div className="video-actions">
                        <button className="btn btn-sm">
                          <i className="fas fa-backward"></i>
                        </button>
                        <button className="btn btn-sm">
                          <i className="fas fa-forward"></i>
                        </button>
                        <button className="btn btn-sm">
                          <i className="fas fa-expand"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="document-viewer">
                    <div className="document-placeholder">
                      <i className="fas fa-file-alt"></i>
                      <p>عارض المستندات</p>
                      <p>{currentContent.title}</p>
                    </div>
                  </div>
                )}

                <div className="content-description">
                  <h3>الوصف</h3>
                  <p>{currentContent.description || 'لا يوجد وصف متاح'}</p>
                </div>

                <div className="content-resources">
                  <h3>الموارد</h3>
                  {currentContent.resources && currentContent.resources.length > 0 ? (
                    <ul className="resources-list">
                      {currentContent.resources.map((resource, index) => (
                        <li key={index}>
                          <a href={resource.url} target="_blank" rel="noopener noreferrer">
                            {resource.title}
                          </a>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p>لا توجد موارد متاحة</p>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="no-content-selected">
              <p>اختر محتوى من القائمة الجانبية لعرضه</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseContent;
