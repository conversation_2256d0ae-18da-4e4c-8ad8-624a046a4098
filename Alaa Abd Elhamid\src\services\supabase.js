import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://lppjracxctonvvpudzty.supabase.co'
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxwcGpyYWN4Y3RvbnZ2cHVkenR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNzI5MzEsImV4cCI6MjA2OTg0ODkzMX0.1ld9RCxBEIgdT1catsXfC9b3rpofgAz8rf19dDlk3nE'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

export default supabase
