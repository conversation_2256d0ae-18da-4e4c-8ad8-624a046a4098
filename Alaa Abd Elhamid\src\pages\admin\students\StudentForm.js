import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useNotification } from '../../../contexts/NotificationContext';
import { db } from '../../../services/firebase';
import { doc, getDoc, addDoc, updateDoc, collection, query, where, getDocs, deleteDoc } from 'firebase/firestore';
import { generateAccessCode } from '../../../utils';

const StudentForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const isEditing = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [student, setStudent] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    isActive: true
  });
  const [accessCodes, setAccessCodes] = useState([]);
  const [showAccessCode, setShowAccessCode] = useState(false);
  const [newAccessCode, setNewAccessCode] = useState('');

  useEffect(() => {
    if (isEditing) {
      const fetchStudent = async () => {
        setLoading(true);
        try {
          const studentDoc = doc(db, 'users', id);
          const studentSnapshot = await getDoc(studentDoc);

          if (studentSnapshot.exists()) {
            const studentData = studentSnapshot.data();
            setStudent({
              id: studentSnapshot.id,
              name: studentData.name || '',
              email: studentData.email || '',
              phone: studentData.phone || '',
              bio: studentData.bio || '',
              isActive: studentData.isActive !== false
            });

            // Fetch access codes for this student
            const accessCodesCollection = collection(db, 'accessCodes');
            const accessCodesQuery = query(
              accessCodesCollection,
              where('studentId', '==', id)
            );
            const accessCodesSnapshot = await getDocs(accessCodesQuery);
            const accessCodesList = accessCodesSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            setAccessCodes(accessCodesList);
          } else {
            showNotification('الطالب غير موجود', 'error');
            navigate('/admin/students');
          }
        } catch (error) {
          showNotification('حدث خطأ أثناء جلب بيانات الطالب', 'error');
          console.error('Error fetching student:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchStudent();
    }
  }, [id, isEditing, navigate, showNotification]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setStudent(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const generateNewAccessCode = () => {
    const code = generateAccessCode();
    setNewAccessCode(code);
    setShowAccessCode(true);
  };

  const saveAccessCode = async () => {
    if (!newAccessCode) return;

    try {
      await addDoc(collection(db, 'accessCodes'), {
        code: newAccessCode,
        studentId: id || 'temp', // Will be updated after creating student if new
        used: false,
        createdAt: new Date()
      });

      showNotification('تم إنشاء رمز الوصول بنجاح', 'success');
      setNewAccessCode('');
      setShowAccessCode(false);

      // Refresh access codes list
      if (id) {
        const accessCodesCollection = collection(db, 'accessCodes');
        const accessCodesQuery = query(
          accessCodesCollection,
          where('studentId', '==', id)
        );
        const accessCodesSnapshot = await getDocs(accessCodesQuery);
        const accessCodesList = accessCodesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        setAccessCodes(accessCodesList);
      }
    } catch (error) {
      showNotification('حدث خطأ أثناء إنشاء رمز الوصول', 'error');
      console.error('Error creating access code:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      let studentId;

      if (isEditing) {
        // Update existing student
        await updateDoc(doc(db, 'users', id), {
          name: student.name,
          phone: student.phone,
          bio: student.bio,
          isActive: student.isActive
        });
        studentId = id;
        showNotification('تم تحديث بيانات الطالب بنجاح', 'success');
      } else {
        // Create new student
        const studentData = {
          name: student.name,
          email: student.email,
          phone: student.phone,
          bio: student.bio,
          role: 'student',
          isActive: student.isActive,
          createdAt: new Date()
        };

        const studentRef = await addDoc(collection(db, 'users'), studentData);
        studentId = studentRef.id;
        showNotification('تم إضافة الطالب بنجاح', 'success');
      }

      // If we generated an access code and it's not saved yet, save it now
      if (showAccessCode && newAccessCode) {
        await addDoc(collection(db, 'accessCodes'), {
          code: newAccessCode,
          studentId: studentId,
          used: false,
          createdAt: new Date()
        });
        setNewAccessCode('');
        setShowAccessCode(false);
      }

      navigate('/admin/students');
    } catch (error) {
      showNotification('حدث خطأ أثناء حفظ بيانات الطالب', 'error');
      console.error('Error saving student:', error);
    } finally {
      setSaving(false);
    }
  };

  const deleteAccessCode = async (codeId) => {
    if (window.confirm('هل أنت متأكد من أنك تريد حذف رمز الوصول هذا؟')) {
      try {
        await deleteDoc(doc(db, 'accessCodes', codeId));
        showNotification('تم حذف رمز الوصول بنجاح', 'success');

        // Refresh access codes list
        const accessCodesCollection = collection(db, 'accessCodes');
        const accessCodesQuery = query(
          accessCodesCollection,
          where('studentId', '==', id)
        );
        const accessCodesSnapshot = await getDocs(accessCodesQuery);
        const accessCodesList = accessCodesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        setAccessCodes(accessCodesList);
      } catch (error) {
        showNotification('حدث خطأ أثناء حذف رمز الوصول', 'error');
        console.error('Error deleting access code:', error);
      }
    }
  };

  if (loading) {
    return <div className="loading-spinner">جاري التحميل...</div>;
  }

  return (
    <div className="student-form">
      <div className="page-header">
        <h2>{isEditing ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'}</h2>
        <button
          className="btn btn-secondary"
          onClick={() => navigate('/admin/students')}
        >
          العودة للقائمة
        </button>
      </div>

      <div className="form-container">
        <form onSubmit={handleSubmit}>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="name">الاسم *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={student.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">البريد الإلكتروني *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={student.email}
                onChange={handleChange}
                required
                disabled={isEditing} // Don't allow editing email after creation
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">رقم الهاتف</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={student.phone}
                onChange={handleChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="isActive">الحالة</label>
              <div className="checkbox-group">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={student.isActive}
                  onChange={handleChange}
                />
                <label htmlFor="isActive">نشط</label>
              </div>
            </div>

            <div className="form-group full-width">
              <label htmlFor="bio">ملاحظات</label>
              <textarea
                id="bio"
                name="bio"
                value={student.bio}
                onChange={handleChange}
                rows="4"
              ></textarea>
            </div>
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={saving}
            >
              {saving ? 'جاري الحفظ...' : (isEditing ? 'تحديث البيانات' : 'إضافة الطالب')}
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => navigate('/admin/students')}
            >
              إلغاء
            </button>
          </div>
        </form>

        {isEditing && (
          <div className="access-codes-section">
            <h3>رموز الوصول</h3>

            <div className="access-codes-actions">
              <button
                className="btn btn-primary"
                onClick={generateNewAccessCode}
              >
                إنشاء رمز وصول جديد
              </button>
            </div>

            {showAccessCode && (
              <div className="new-access-code">
                <div className="access-code-display">
                  <span>{newAccessCode}</span>
                  <button
                    className="btn btn-success"
                    onClick={saveAccessCode}
                  >
                    حفظ الرمز
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowAccessCode(false);
                      setNewAccessCode('');
                    }}
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            )}

            <div className="access-codes-list">
              {accessCodes.length === 0 ? (
                <p>لا توجد رموز وصول متاحة لهذا الطالب</p>
              ) : (
                <div className="codes-table">
                  <table>
                    <thead>
                      <tr>
                        <th>رمز الوصول</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {accessCodes.map(code => (
                        <tr key={code.id}>
                          <td className="code">{code.code}</td>
                          <td>
                            <span className={`status ${code.used ? 'used' : 'available'}`}>
                              {code.used ? 'مستخدم' : 'متاح'}
                            </span>
                          </td>
                          <td>{code.createdAt ? new Date(code.createdAt.toDate()).toLocaleDateString('ar-EG') : 'غير محدد'}</td>
                          <td className="actions-cell">
                            <button
                              className="btn btn-danger"
                              onClick={() => deleteAccessCode(code.id)}
                            >
                              حذف
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentForm;
