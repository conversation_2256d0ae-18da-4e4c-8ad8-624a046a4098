# إعداد قاعدة البيانات Supabase - منصة علاء عبد الحميد التعليمية

## معلومات المشروع

**اسم المشروع:** alaa-academy-platform  
**معرف المشروع:** lppjracxctonvvpudzty  
**الرابط:** https://lppjracxctonvvpudzty.supabase.co  
**المنطقة:** us-east-1

## مفاتيح API

### مفتاح Anon (للاستخدام في التطبيق)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxwcGpyYWN4Y3RvbnZ2cHVkenR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNzI5MzEsImV4cCI6MjA2OTg0ODkzMX0.1ld9RCxBEIgdT1catsXfC9b3rpofgAz8rf19dDlk3nE
```

### مفتاح Service Role (للعمليات الإدارية)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxwcGpyYWN4Y3RvbnZ2cHVkenR5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDI3MjkzMSwiZXhwIjoyMDY5ODQ4OTMxfQ.btLhJm2-LBn8qub6JD-5CvsPJzjVJlzTaavipbkESRM
```

## الجداول المنشأة

### 1. users - المستخدمين
- `id` (UUID) - معرف المستخدم
- `email` (VARCHAR) - البريد الإلكتروني
- `name` (VARCHAR) - الاسم
- `role` (VARCHAR) - الدور (admin/student)
- `phone` (VARCHAR) - رقم الهاتف
- `bio` (TEXT) - نبذة شخصية
- `avatar_url` (TEXT) - رابط الصورة الشخصية
- `is_active` (BOOLEAN) - حالة النشاط

### 2. courses - الدورات
- `id` (UUID) - معرف الدورة
- `title` (VARCHAR) - عنوان الدورة
- `description` (TEXT) - وصف الدورة
- `instructor` (VARCHAR) - اسم المدرس
- `category` (VARCHAR) - فئة الدورة
- `level` (VARCHAR) - مستوى الدورة
- `duration` (INTEGER) - مدة الدورة بالأيام
- `price` (DECIMAL) - سعر الدورة

### 3. materials - المواد التعليمية
- `id` (UUID) - معرف المادة
- `course_id` (UUID) - معرف الدورة
- `title` (VARCHAR) - عنوان المادة
- `type` (VARCHAR) - نوع المادة (video/pdf/document/link)
- `file_url` (TEXT) - رابط الملف
- `video_url` (TEXT) - رابط الفيديو
- `duration` (INTEGER) - مدة الفيديو بالثواني
- `order_index` (INTEGER) - ترتيب المادة

### 4. enrollments - التسجيلات
- `id` (UUID) - معرف التسجيل
- `student_id` (UUID) - معرف الطالب
- `course_id` (UUID) - معرف الدورة
- `progress` (DECIMAL) - نسبة التقدم (0-100)
- `enrolled_at` (TIMESTAMP) - تاريخ التسجيل
- `completed_at` (TIMESTAMP) - تاريخ الإكمال

### 5. student_progress - تقدم الطلاب
- `id` (UUID) - معرف التقدم
- `student_id` (UUID) - معرف الطالب
- `material_id` (UUID) - معرف المادة
- `course_id` (UUID) - معرف الدورة
- `completed` (BOOLEAN) - حالة الإكمال
- `watch_time` (INTEGER) - وقت المشاهدة بالثواني

### 6. exams - الامتحانات
- `id` (UUID) - معرف الامتحان
- `course_id` (UUID) - معرف الدورة
- `title` (VARCHAR) - عنوان الامتحان
- `duration` (INTEGER) - مدة الامتحان بالدقائق
- `total_marks` (INTEGER) - إجمالي الدرجات
- `passing_marks` (INTEGER) - درجة النجاح

### 7. exam_questions - أسئلة الامتحانات
- `id` (UUID) - معرف السؤال
- `exam_id` (UUID) - معرف الامتحان
- `question` (TEXT) - نص السؤال
- `option_a` (TEXT) - الخيار أ
- `option_b` (TEXT) - الخيار ب
- `option_c` (TEXT) - الخيار ج
- `option_d` (TEXT) - الخيار د
- `correct_answer` (CHAR) - الإجابة الصحيحة

### 8. exam_results - نتائج الامتحانات
- `id` (UUID) - معرف النتيجة
- `exam_id` (UUID) - معرف الامتحان
- `student_id` (UUID) - معرف الطالب
- `score` (INTEGER) - النتيجة
- `percentage` (DECIMAL) - النسبة المئوية
- `passed` (BOOLEAN) - حالة النجاح
- `answers` (JSONB) - إجابات الطالب

### 9. certificates - الشهادات
- `id` (UUID) - معرف الشهادة
- `student_id` (UUID) - معرف الطالب
- `course_id` (UUID) - معرف الدورة
- `certificate_number` (VARCHAR) - رقم الشهادة
- `issued_date` (DATE) - تاريخ الإصدار
- `certificate_url` (TEXT) - رابط الشهادة

### 10. messages - الرسائل
- `id` (UUID) - معرف الرسالة
- `sender_id` (UUID) - معرف المرسل
- `receiver_id` (UUID) - معرف المستقبل
- `message` (TEXT) - نص الرسالة
- `is_read` (BOOLEAN) - حالة القراءة

### 11. notifications - الإشعارات
- `id` (UUID) - معرف الإشعار
- `user_id` (UUID) - معرف المستخدم
- `title` (VARCHAR) - عنوان الإشعار
- `message` (TEXT) - نص الإشعار
- `type` (VARCHAR) - نوع الإشعار
- `is_read` (BOOLEAN) - حالة القراءة

### 12. access_codes - أكواد الوصول
- `id` (UUID) - معرف الكود
- `code` (VARCHAR) - الكود المكون من 7 أرقام
- `student_id` (UUID) - معرف الطالب
- `is_used` (BOOLEAN) - حالة الاستخدام
- `used_at` (TIMESTAMP) - تاريخ الاستخدام
- `expires_at` (TIMESTAMP) - تاريخ انتهاء الصلاحية

## Storage Buckets

### 1. videos - الفيديوهات
- **الوصول:** خاص (للطلاب المسجلين فقط)
- **الأنواع المسموحة:** mp4, webm, ogg

### 2. documents - المستندات
- **الوصول:** خاص (للطلاب المسجلين فقط)
- **الأنواع المسموحة:** pdf, doc, docx

### 3. images - الصور
- **الوصول:** عام
- **الأنواع المسموحة:** jpg, jpeg, png, gif

### 4. certificates - الشهادات
- **الوصول:** خاص (للطالب المالك فقط)
- **الأنواع المسموحة:** pdf

## Functions المنشأة

### 1. generate_access_code()
- **الوظيفة:** توليد كود وصول مكون من 7 أرقام
- **الاستخدام:** `SELECT generate_access_code();`

### 2. calculate_course_progress(student_uuid, course_uuid)
- **الوظيفة:** حساب تقدم الطالب في دورة معينة
- **الاستخدام:** `SELECT calculate_course_progress('student_id', 'course_id');`

### 3. generate_certificate_number()
- **الوظيفة:** توليد رقم شهادة فريد
- **الاستخدام:** `SELECT generate_certificate_number();`

## Real-time Subscriptions

تم تفعيل Real-time للجداول التالية:
- `messages` - للدردشة المباشرة
- `notifications` - للإشعارات المباشرة
- `student_progress` - لتتبع التقدم المباشر
- `enrollments` - لتحديثات التسجيل

## Row Level Security (RLS)

تم تفعيل RLS على جميع الجداول مع سياسات أمان مناسبة:
- المستخدمون يمكنهم رؤية بياناتهم الشخصية فقط
- الطلاب يمكنهم رؤية محتوى الدورات المسجلين بها فقط
- المديرون لديهم صلاحيات كاملة
- الرسائل والإشعارات محمية حسب المالك

## Views للإحصائيات

### 1. course_stats
- إحصائيات الدورات (عدد الطلاب، التقدم، الشهادات)

### 2. student_course_progress
- تقدم الطلاب في الدورات المختلفة
