import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { db } from '../../services/firebase';
import { doc, getDoc, collection, getDocs, query, where, orderBy, addDoc, updateDoc, arrayUnion } from 'firebase/firestore';

const ExamPage = ({ setPageTitle }) => {
  const { currentUser } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const { courseId, examId } = useParams();

  const [exam, setExam] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [examStarted, setExamStarted] = useState(false);
  const [examCompleted, setExamCompleted] = useState(false);
  const [result, setResult] = useState(null);

  useEffect(() => {
    setPageTitle('الامتحان');

    const fetchExamData = async () => {
      try {
        // Fetch exam details
        const examDoc = doc(db, 'exams', examId);
        const examSnapshot = await getDoc(examDoc);

        if (!examSnapshot.exists()) {
          showNotification('الامتحان غير موجود', 'error');
          navigate(`/student/courses/${courseId}`);
          return;
        }

        const examData = examSnapshot.data();
        setExam({
          id: examSnapshot.id,
          ...examData
        });

        // Check if student has already taken this exam
        const resultsCollection = collection(db, 'examResults');
        const resultsQuery = query(
          resultsCollection,
          where('examId', '==', examId),
          where('studentId', '==', currentUser.uid)
        );
        const resultsSnapshot = await getDocs(resultsQuery);

        if (!resultsSnapshot.empty) {
          const resultData = resultsSnapshot.docs[0].data();
          setResult(resultData);
          setExamCompleted(true);
          setLoading(false);
          return;
        }

        // Fetch exam questions
        const questionsCollection = collection(db, 'questions');
        const questionsQuery = query(
          questionsCollection,
          where('examId', '==', examId),
          orderBy('order', 'asc')
        );
        const questionsSnapshot = await getDocs(questionsQuery);

        const questionsList = questionsSnapshot.docs.map(questionDoc => ({
          id: questionDoc.id,
          ...questionDoc.data()
        }));

        setQuestions(questionsList);

        // Initialize answers object
        const initialAnswers = {};
        questionsList.forEach(question => {
          initialAnswers[question.id] = '';
        });
        setAnswers(initialAnswers);

        // Set time left (convert minutes to seconds)
        setTimeLeft(examData.duration * 60);
      } catch (error) {
        showNotification('حدث خطأ أثناء جلب بيانات الامتحان', 'error');
        console.error('Error fetching exam data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchExamData();
  }, [setPageTitle, courseId, examId, navigate, showNotification, currentUser.uid]);

  useEffect(() => {
    let timer;

    if (examStarted && !examCompleted && timeLeft > 0) {
      timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0 && examStarted && !examCompleted) {
      // Auto-submit when time runs out
      handleSubmitExam();
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [examStarted, examCompleted, timeLeft]);

  const handleAnswerChange = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const startExam = () => {
    setExamStarted(true);
  };

  const handleSubmitExam = async () => {
    if (submitting) return;

    setSubmitting(true);

    try {
      // Calculate score
      let correctAnswers = 0;
      questions.forEach(question => {
        if (answers[question.id] === question.correctAnswer) {
          correctAnswers++;
        }
      });

      const score = Math.round((correctAnswers / questions.length) * 100);

      // Save exam result
      const resultData = {
        examId,
        courseId,
        studentId: currentUser.uid,
        answers,
        score,
        correctAnswers,
        totalQuestions: questions.length,
        completedAt: new Date(),
        timeSpent: exam.duration * 60 - timeLeft
      };

      await addDoc(collection(db, 'examResults'), resultData);

      // Update student progress
      await updateDoc(doc(db, 'users', currentUser.uid), {
        examResults: arrayUnion({
          examId,
          courseId,
          score,
          completedAt: new Date()
        })
      });

      setResult(resultData);
      setExamCompleted(true);
      showNotification('تم إرسال الامتحان بنجاح', 'success');
    } catch (error) {
      showNotification('حدث خطأ أثناء إرسال الامتحان', 'error');
      console.error('Error submitting exam:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return <div className="loading-spinner">جاري التحميل...</div>;
  }

  if (!exam) {
    return <div className="error-message">لم يتم العثور على الامتحان</div>;
  }

  return (
    <div className="exam-page">
      <div className="exam-header">
        <h1>{exam.title}</h1>
        <button
          className="btn btn-secondary"
          onClick={() => navigate(`/student/courses/${courseId}`)}
        >
          العودة للدورة
        </button>
      </div>

      {examCompleted ? (
        <div className="exam-result">
          <div className="result-card">
            <h2>نتيجة الامتحان</h2>
            <div className="score-circle">
              <span className="score">{result.score}%</span>
            </div>
            <div className="result-details">
              <p>الإجابات الصحيحة: {result.correctAnswers} من {result.totalQuestions}</p>
              <p>الوقت المستغرق: {Math.floor(result.timeSpent / 60)} دقيقة {result.timeSpent % 60} ثانية</p>
              <p>تاريخ الإنجاز: {new Date(result.completedAt).toLocaleDateString('ar-EG')}</p>
            </div>
            <div className="result-actions">
              <button
                className="btn btn-primary"
                onClick={() => navigate(`/student/courses/${courseId}`)}
              >
                العودة للدورة
              </button>
            </div>
          </div>
        </div>
      ) : (
        <>
          {!examStarted ? (
            <div className="exam-instructions">
              <div className="instructions-card">
                <h2>تعليمات الامتحان</h2>
                <div className="instructions-content">
                  <p>مدة الامتحان: {exam.duration} دقيقة</p>
                  <p>عدد الأسئلة: {questions.length}</p>
                  <p>نوع الامتحان: {exam.type === 'multiple-choice' ? 'اختيار من متعدد' : 'أسئلة متنوعة'}</p>
                  <div className="instructions-list">
                    <h3>ملاحظات هامة:</h3>
                    <ul>
                      <li>بمجرد بدء الامتحان، لا يمكنك إيقافه مؤقتًا</li>
                      <li>سيتم إرسال الإجابات تلقائيًا عند انتهاء الوقت</li>
                      <li>تأكد من الإجابة على جميع الأسئلة قبل الإرسال</li>
                      <li>لا يمكنك إعادة الامتحان مرة أخرى</li>
                    </ul>
                  </div>
                </div>
                <div className="instructions-actions">
                  <button
                    className="btn btn-primary"
                    onClick={startExam}
                  >
                    بدء الامتحان
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="exam-content">
              <div className="exam-timer">
                <div className="timer-display">
                  <i className="fas fa-clock"></i>
                  <span>{formatTime(timeLeft)}</span>
                </div>
                <div className="timer-progress">
                  <div
                    className="progress-fill"
                    style={{ 
                      width: `${((exam.duration * 60 - timeLeft) / (exam.duration * 60)) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>

              <div className="questions-container">
                {questions.map((question, index) => (
                  <div key={question.id} className="question-card">
                    <div className="question-header">
                      <h3>السؤال {index + 1}</h3>
                      <span className="question-score">
                        {question.points || 1} نقطة
                      </span>
                    </div>
                    <div className="question-text">
                      <p>{question.text}</p>
                    </div>
                    <div className="question-options">
                      {question.options.map((option, optionIndex) => (
                        <div key={optionIndex} className="option">
                          <input
                            type="radio"
                            id={`question-${question.id}-option-${optionIndex}`}
                            name={`question-${question.id}`}
                            value={option}
                            checked={answers[question.id] === option}
                            onChange={() => handleAnswerChange(question.id, option)}
                          />
                          <label htmlFor={`question-${question.id}-option-${optionIndex}`}>
                            {option}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="exam-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleSubmitExam}
                  disabled={submitting}
                >
                  {submitting ? 'جاري الإرسال...' : 'إرسال الامتحان'}
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ExamPage;
