import { useState, useEffect } from 'react';
import { db } from '../services/firebase';

// Custom hook for fetching documents from Firestore
export const useFirestore = (collection, queryOptions = null) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let query = db.collection(collection);

    // Apply query options if provided
    if (queryOptions) {
      if (queryOptions.where) {
        queryOptions.where.forEach(option => {
          query = query.where(...option);
        });
      }

      if (queryOptions.orderBy) {
        query = query.orderBy(...queryOptions.orderBy);
      }

      if (queryOptions.limit) {
        query = query.limit(queryOptions.limit);
      }
    }

    const unsubscribe = query.onSnapshot(
      (snapshot) => {
        const results = [];
        snapshot.docs.forEach(doc => {
          results.push({ id: doc.id, ...doc.data() });
        });
        setDocuments(results);
        setLoading(false);
        setError(null);
      },
      (err) => {
        console.error(err);
        setError('فشل في جلب البيانات');
        setLoading(false);
      }
    );

    // Cleanup on unmount
    return () => unsubscribe();
  }, [collection, queryOptions]);

  return { documents, loading, error };
};

// Custom hook for fetching a single document from Firestore
export const useFirestoreDoc = (collection, docId) => {
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!docId) {
      setLoading(false);
      return;
    }

    const unsubscribe = db.collection(collection).doc(docId).onSnapshot(
      (doc) => {
        if (doc.exists) {
          setDocument({ id: doc.id, ...doc.data() });
        } else {
          setDocument(null);
        }
        setLoading(false);
        setError(null);
      },
      (err) => {
        console.error(err);
        setError('فشل في جلب البيانات');
        setLoading(false);
      }
    );

    // Cleanup on unmount
    return () => unsubscribe();
  }, [collection, docId]);

  return { document, loading, error };
};

// Custom hook for adding a document to Firestore
export const useAddDocument = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const addDocument = async (collection, data) => {
    setLoading(true);
    setError(null);

    try {
      const docRef = await db.collection(collection).add({
        ...data,
        createdAt: new Date()
      });
      setLoading(false);
      return { success: true, id: docRef.id };
    } catch (err) {
      console.error(err);
      setError('فشل في إضافة البيانات');
      setLoading(false);
      return { success: false, error: err.message };
    }
  };

  return { addDocument, loading, error };
};

// Custom hook for updating a document in Firestore
export const useUpdateDocument = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const updateDocument = async (collection, docId, data) => {
    setLoading(true);
    setError(null);

    try {
      await db.collection(collection).doc(docId).update({
        ...data,
        updatedAt: new Date()
      });
      setLoading(false);
      return { success: true };
    } catch (err) {
      console.error(err);
      setError('فشل في تحديث البيانات');
      setLoading(false);
      return { success: false, error: err.message };
    }
  };

  return { updateDocument, loading, error };
};

// Custom hook for deleting a document from Firestore
export const useDeleteDocument = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const deleteDocument = async (collection, docId) => {
    setLoading(true);
    setError(null);

    try {
      await db.collection(collection).doc(docId).delete();
      setLoading(false);
      return { success: true };
    } catch (err) {
      console.error(err);
      setError('فشل في حذف البيانات');
      setLoading(false);
      return { success: false, error: err.message };
    }
  };

  return { deleteDocument, loading, error };
};
