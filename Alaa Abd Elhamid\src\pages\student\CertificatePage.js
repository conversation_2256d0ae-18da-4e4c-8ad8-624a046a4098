import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { db } from '../../services/firebase';
import { doc, getDoc, getDocs, collection, query, where, addDoc, updateDoc } from 'firebase/firestore';

const CertificatePage = ({ setPageTitle }) => {
  const { currentUser } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const { courseId } = useParams();

  const [course, setCourse] = useState(null);
  const [certificate, setCertificate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    setPageTitle('شهادة إتمام الدورة');

    const fetchCertificateData = async () => {
      try {
        // Fetch course details
        const courseDoc = doc(db, 'courses', courseId);
        const courseSnapshot = await getDoc(courseDoc);

        if (!courseSnapshot.exists()) {
          showNotification('الدورة غير موجودة', 'error');
          navigate('/student/courses');
          return;
        }

        const courseData = courseSnapshot.data();
        setCourse({
          id: courseSnapshot.id,
          ...courseData
        });

        // Check if student has completed the course
        const progress = courseData.studentsProgress?.[currentUser.uid] || 0;

        if (progress < 100) {
          showNotification('يجب إكمال جميع مواد الدورة للحصول على شهادة', 'error');
          navigate(`/student/courses/${courseId}`);
          return;
        }

        // Check if certificate already exists
        const certificatesCollection = collection(db, 'certificates');
        const certificatesQuery = query(
          certificatesCollection,
          where('courseId', '==', courseId),
          where('studentId', '==', currentUser.uid)
        );
        const certificatesSnapshot = await getDocs(certificatesQuery);

        if (!certificatesSnapshot.empty) {
          const certificateData = certificatesSnapshot.docs[0].data();
          setCertificate({
            id: certificatesSnapshot.docs[0].id,
            ...certificateData
          });
        }
      } catch (error) {
        showNotification('حدث خطأ أثناء جلب بيانات الشهادة', 'error');
        console.error('Error fetching certificate data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCertificateData();
  }, [setPageTitle, courseId, navigate, showNotification, currentUser.uid]);

  const generateCertificate = async () => {
    if (generating) return;

    setGenerating(true);

    try {
      // Generate certificate data
      const certificateData = {
        courseId,
        courseTitle: course.title,
        studentId: currentUser.uid,
        studentName: currentUser.name || 'طالب',
        instructorName: course.instructor,
        issueDate: new Date(),
        certificateId: `CERT-${courseId.substring(0, 4)}-${currentUser.uid.substring(0, 4)}-${Date.now().toString().slice(-4)}`
      };

      // Save certificate to Firestore
      const certificateRef = await addDoc(collection(db, 'certificates'), certificateData);

      setCertificate({
        id: certificateRef.id,
        ...certificateData
      });

      showNotification('تم إنشاء الشهادة بنجاح', 'success');
    } catch (error) {
      showNotification('حدث خطأ أثناء إنشاء الشهادة', 'error');
      console.error('Error generating certificate:', error);
    } finally {
      setGenerating(false);
    }
  };

  const downloadCertificate = async () => {
    if (downloading) return;

    setDownloading(true);

    try {
      // In a real app, you would generate a PDF certificate here
      // For now, we'll just show a success message

      // Update certificate download count
      await updateDoc(doc(db, 'certificates', certificate.id), {
        downloadedAt: new Date(),
        downloadCount: (certificate.downloadCount || 0) + 1
      });

      showNotification('تم تحميل الشهادة بنجاح', 'success');
    } catch (error) {
      showNotification('حدث خطأ أثناء تحميل الشهادة', 'error');
      console.error('Error downloading certificate:', error);
    } finally {
      setDownloading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return <div className="loading-spinner">جاري التحميل...</div>;
  }

  if (!course) {
    return <div className="error-message">لم يتم العثور على الدورة</div>;
  }

  return (
    <div className="certificate-page">
      <div className="certificate-header">
        <h1>شهادة إتمام الدورة</h1>
        <button
          className="btn btn-secondary"
          onClick={() => navigate(`/student/courses/${courseId}`)}
        >
          العودة للدورة
        </button>
      </div>

      <div className="certificate-container">
        {certificate ? (
          <div className="certificate-preview">
            <div className="certificate-document">
              <div className="certificate-border">
                <div className="certificate-content">
                  <div className="certificate-header">
                    <h2>شهادة إتمام دورة</h2>
                    <div className="certificate-logo">
                      <img src="/assets/images/logo.svg" alt="Logo" />
                    </div>
                  </div>

                  <div className="certificate-body">
                    <p className="certificate-text">
                      تقر هذه الشهادة بأن
                    </p>
                    <h3 className="student-name">{certificate.studentName}</h3>
                    <p className="certificate-text">
                      قد أكمل بنجاح دورة
                    </p>
                    <h3 className="course-title">{certificate.courseTitle}</h3>
                    <p className="certificate-text">
                      التي قدمها المدرب
                    </p>
                    <h3 className="instructor-name">{certificate.instructorName}</h3>
                  </div>

                  <div className="certificate-footer">
                    <div className="certificate-date">
                      <p>تاريخ الإصدار</p>
                      <p>{formatDate(certificate.issueDate)}</p>
                    </div>
                    <div className="certificate-id">
                      <p>رقم الشهادة</p>
                      <p>{certificate.certificateId}</p>
                    </div>
                  </div>

                  <div className="certificate-signatures">
                    <div className="signature">
                      <div className="signature-line"></div>
                      <p>مدير المنصة التعليمية</p>
                    </div>
                    <div className="signature">
                      <div className="signature-line"></div>
                      <p>المدرب</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="certificate-actions">
              <button
                className="btn btn-primary"
                onClick={downloadCertificate}
                disabled={downloading}
              >
                {downloading ? 'جاري التحميل...' : 'تحميل الشهادة'}
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => navigate(`/student/courses/${courseId}`)}
              >
                العودة للدورة
              </button>
            </div>
          </div>
        ) : (
          <div className="certificate-generation">
            <div className="generation-card">
              <h2>تهانينا!</h2>
              <p>لقد أكملت جميع مواد دورة "{course.title}" بنجاح</p>
              <p>يمكنك الآن إنشاء شهادة إتمام الدورة</p>

              <div className="generation-actions">
                <button
                  className="btn btn-primary"
                  onClick={generateCertificate}
                  disabled={generating}
                >
                  {generating ? 'جاري الإنشاء...' : 'إنشاء الشهادة'}
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => navigate(`/student/courses/${courseId}`)}
                >
                  العودة للدورة
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CertificatePage;
