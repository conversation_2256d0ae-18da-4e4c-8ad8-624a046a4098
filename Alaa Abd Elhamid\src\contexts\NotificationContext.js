import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

const NotificationContext = createContext();

export function useNotifications() {
  return useContext(NotificationContext);
}

// Alias for backward compatibility
export const useNotification = useNotifications;

export function NotificationProvider({ children }) {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const { currentUser } = useAuth();

  useEffect(() => {
    if (!currentUser) return;

    // Mock notifications for demonstration
    const mockNotifications = [
      {
        id: '1',
        title: 'مرحباً بك في المنصة',
        message: 'نرحب بك في منصة علاء عبد الحميد التعليمية',
        type: 'info',
        read: false,
        createdAt: new Date()
      }
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.read).length);

        snapshot.forEach(doc => {
          const notification = {
            id: doc.id,
            ...doc.data()
          };

          notificationsData.push(notification);

          if (!notification.read) {
            unread++;
          }
        });

        setNotifications(notificationsData);
        setUnreadCount(unread);
      });

    return unsubscribe;
  }, [currentUser]);

  const markAsRead = async (notificationId) => {
    try {
      await db.collection('notifications').doc(notificationId).update({
        read: true,
        readAt: new Date()
      });
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error('حدث خطأ أثناء تحديث الإشعار');
    }
  };

  const markAllAsRead = async () => {
    try {
      const batch = db.batch();
      const unreadNotifications = notifications.filter(n => !n.read);

      unreadNotifications.forEach(notification => {
        const notificationRef = db.collection('notifications').doc(notification.id);
        batch.update(notificationRef, {
          read: true,
          readAt: new Date()
        });
      });

      await batch.commit();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error('حدث خطأ أثناء تحديث الإشعارات');
    }
  };

  const addNotification = async (userId, title, message, type = 'info') => {
    try {
      await db.collection('notifications').add({
        userId,
        title,
        message,
        type,
        read: false,
        createdAt: new Date()
      });
    } catch (error) {
      console.error("Error adding notification:", error);
    }
  };

  const deleteNotification = async (notificationId) => {
    try {
      await db.collection('notifications').doc(notificationId).delete();
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error('حدث خطأ أثناء حذف الإشعار');
    }
  };

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    addNotification,
    deleteNotification
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}
