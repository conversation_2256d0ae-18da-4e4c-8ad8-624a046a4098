// Supabase configuration and setup
export const SUPABASE_CONFIG = {
  url: process.env.REACT_APP_SUPABASE_URL || 'https://lppjracxctonvvpudzty.supabase.co',
  anonKey: process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxwcGpyYWN4Y3RvbnZ2cHVkenR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNzI5MzEsImV4cCI6MjA2OTg0ODkzMX0.1ld9RCxBEIgdT1catsXfC9b3rpofgAz8rf19dDlk3nE'
};

// Database table names
export const TABLES = {
  USERS: 'users',
  COURSES: 'courses',
  MATERIALS: 'materials',
  ENROLLMENTS: 'enrollments',
  STUDENT_PROGRESS: 'student_progress',
  EXAMS: 'exams',
  EXAM_QUESTIONS: 'exam_questions',
  EXAM_RESULTS: 'exam_results',
  CERTIFICATES: 'certificates',
  MESSAGES: 'messages',
  NOTIFICATIONS: 'notifications',
  ACCESS_CODES: 'access_codes'
};

// Storage bucket names
export const STORAGE_BUCKETS = {
  VIDEOS: 'videos',
  DOCUMENTS: 'documents',
  IMAGES: 'images',
  CERTIFICATES: 'certificates'
};

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  STUDENT: 'student'
};

// Course levels
export const COURSE_LEVELS = {
  BEGINNER: 'مبتدئ',
  INTERMEDIATE: 'متوسط',
  ADVANCED: 'متقدم'
};

// Material types
export const MATERIAL_TYPES = {
  VIDEO: 'video',
  PDF: 'pdf',
  DOCUMENT: 'document',
  LINK: 'link'
};

// Notification types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
};

// Real-time channels
export const REALTIME_CHANNELS = {
  MESSAGES: 'messages',
  NOTIFICATIONS: 'notifications',
  STUDENT_PROGRESS: 'student_progress'
};
