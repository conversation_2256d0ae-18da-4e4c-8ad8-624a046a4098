import React, { useState, useEffect, useRef } from 'react';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { collection, getDocs, query, where, orderBy, addDoc, onSnapshot } from 'firebase/firestore';

const Chat = ({ setPageTitle }) => {
  const { currentUser } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const messagesEndRef = useRef(null);
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    setPageTitle('الدردشة');
    fetchStudents();

    // Cleanup on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [setPageTitle]);

  useEffect(() => {
    if (selectedStudent) {
      fetchMessages();
    } else {
      // Clear messages when no student is selected
      setMessages([]);
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    }
  }, [selectedStudent]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchStudents = async () => {
    try {
      const studentsSnapshot = await getDocs(collection(db, 'students'));
      const studentsData = studentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setStudents(studentsData);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching students:", error);
      toast.error('حدث خطأ أثناء جلب قائمة الطلاب');
      setLoading(false);
    }
  };

  const fetchMessages = () => {
    try {
      const messagesQuery = query(
        collection(db, 'messages'),
        where('adminId', '==', currentUser.uid),
        where('studentId', '==', selectedStudent.id),
        orderBy('timestamp', 'asc')
      );

      // Clean up previous listener if exists
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }

      // Set up real-time listener
      unsubscribeRef.current = onSnapshot(messagesQuery, (snapshot) => {
        const messagesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        setMessages(messagesData);
      });
    } catch (error) {
      console.error("Error fetching messages:", error);
      toast.error('حدث خطأ أثناء جلب الرسائل');
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedStudent) return;

    try {
      await addDoc(collection(db, 'messages'), {
        text: newMessage,
        senderId: currentUser.uid,
        senderRole: 'admin',
        receiverId: selectedStudent.id,
        adminId: currentUser.uid,
        studentId: selectedStudent.id,
        timestamp: new Date(),
        read: false
      });

      setNewMessage('');
      // The message will be automatically fetched due to the onSnapshot listener
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error('حدث خطأ أثناء إرسال الرسالة');
    }
  };

  if (loading) {
    return <div className="loading">جاري التحميل...</div>;
  }

  return (
    <div className="chat-container">
      <div className="chat-sidebar">
        <h3>الطلاب</h3>
        <div className="students-list">
          {students.length > 0 ? (
            students.map(student => (
              <div 
                key={student.id} 
                className={`student-item ${selectedStudent?.id === student.id ? 'active' : ''}`}
                onClick={() => setSelectedStudent(student)}
              >
                <div className="student-avatar">
                  {student.name.charAt(0)}
                </div>
                <div className="student-info">
                  <h4>{student.name}</h4>
                  <p>{student.email}</p>
                </div>
              </div>
            ))
          ) : (
            <p>لا يوجد طلاب</p>
          )}
        </div>
      </div>

      <div className="chat-main">
        {selectedStudent ? (
          <>
            <div className="chat-header">
              <div className="student-info">
                <h3>{selectedStudent.name}</h3>
                <p>{selectedStudent.email}</p>
              </div>
            </div>

            <div className="messages-container">
              {messages.length > 0 ? (
                messages.map(message => (
                  <div 
                    key={message.id} 
                    className={`message ${message.senderRole === 'admin' ? 'sent' : 'received'}`}
                  >
                    <div className="message-content">
                      {message.text}
                    </div>
                    <div className="message-time">
                      {message.timestamp && new Date(message.timestamp.toDate()).toLocaleTimeString()}
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-messages">
                  لا توجد رسائل. ابدأ المحادثة الآن!
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            <form className="message-form" onSubmit={handleSendMessage}>
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="اكتب رسالتك..."
                required
              />
              <button type="submit" className="btn btn-primary">
                إرسال
              </button>
            </form>
          </>
        ) : (
          <div className="no-student-selected">
            <p>اختر طالباً لبدء المحادثة</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
