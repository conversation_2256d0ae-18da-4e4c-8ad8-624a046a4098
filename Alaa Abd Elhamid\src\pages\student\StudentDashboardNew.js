import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { db } from '../../services/firebase';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';

const StudentDashboard = ({ setPageTitle }) => {
  const { currentUser } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [recentCourses, setRecentCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setPageTitle('لوحة التحكم');

    const fetchDashboardData = async () => {
      try {
        // Fetch enrolled courses
        const coursesCollection = collection(db, 'courses');
        const enrolledCoursesQuery = query(
          coursesCollection,
          where('students', 'array-contains', currentUser.uid),
          orderBy('enrolledAt', 'desc')
        );
        const enrolledCoursesSnapshot = await getDocs(enrolledCoursesQuery);
        const enrolledCoursesList = enrolledCoursesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        setEnrolledCourses(enrolledCoursesList);

        // Fetch recent courses (not enrolled)
        const recentCoursesQuery = query(
          coursesCollection,
          orderBy('createdAt', 'desc'),
          limit(5)
        );
        const recentCoursesSnapshot = await getDocs(recentCoursesQuery);
        const recentCoursesList = recentCoursesSnapshot.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          .filter(course => !enrolledCoursesList.some(enrolled => enrolled.id === course.id));

        setRecentCourses(recentCoursesList);
      } catch (error) {
        showNotification('حدث خطأ أثناء جلب البيانات', 'error');
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [setPageTitle, currentUser, showNotification]);

  const handleEnrollCourse = async (courseId) => {
    try {
      // In a real implementation, you would update the course document to add the student
      // to the students array and set the enrolledAt date

      showNotification('تم التسجيل في الدورة بنجاح', 'success');

      // Refresh the data
      window.location.reload();
    } catch (error) {
      showNotification('حدث خطأ أثناء التسجيل في الدورة', 'error');
      console.error('Error enrolling in course:', error);
    }
  };

  const handleViewCourseContent = (courseId) => {
    navigate(`/student/courses/${courseId}/content`);
  };

  const handleViewCourseExams = (courseId) => {
    navigate(`/student/courses/${courseId}/exams`);
  };

  const handleViewCourseCertificate = (courseId) => {
    navigate(`/student/courses/${courseId}/certificate`);
  };

  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    return new Date(date).toLocaleDateString('ar-EG');
  };

  if (loading) {
    return <div className="loading-spinner">جاري التحميل...</div>;
  }

  return (
    <div className="student-dashboard">
      <h1>مرحباً بك في لوحة التحكم</h1>
      <p className="welcome-message">
        {currentUser?.name ? `مرحباً ${currentUser.name}` : 'مرحباً بك'}، آخر تسجيل دخول كان {new Date().toLocaleDateString('ar-EG')}
      </p>

      <div className="dashboard-sections">
        <div className="section enrolled-courses">
          <div className="section-header">
            <h2>دوراتي ({enrolledCourses.length})</h2>
            <button
              className="btn btn-primary"
              onClick={() => navigate('/student/courses')}
            >
              عرض جميع الدورات
            </button>
          </div>

          {enrolledCourses.length === 0 ? (
            <div className="empty-state">
              <p>لم تقم بالتسجيل في أي دورة بعد</p>
              <button
                className="btn btn-primary"
                onClick={() => navigate('/courses')}
              >
                استكشف الدورات
              </button>
            </div>
          ) : (
            <div className="courses-grid">
              {enrolledCourses.slice(0, 3).map(course => (
                <div key={course.id} className="course-card">
                  <div className="course-image">
                    {course.imageUrl ? (
                      <img src={course.imageUrl} alt={course.title} />
                    ) : (
                      <div className="course-placeholder">
                        <i className="fas fa-book"></i>
                      </div>
                    )}
                  </div>
                  <div className="course-content">
                    <h3>{course.title}</h3>
                    <p>{course.description.substring(0, 100)}...</p>
                    <div className="course-meta">
                      <span className="instructor">{course.instructor}</span>
                      <span className="duration">{course.duration} يوم</span>
                    </div>
                    <div className="course-progress">
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${course.progress || 0}%` }}
                        ></div>
                      </div>
                      <span className="progress-text">{course.progress || 0}%</span>
                    </div>
                    <div className="course-actions">
                      <button
                        className="btn btn-primary"
                        onClick={() => handleViewCourseContent(course.id)}
                      >
                        المحتوى
                      </button>
                      <button
                        className="btn btn-info"
                        onClick={() => handleViewCourseExams(course.id)}
                      >
                        الامتحانات
                      </button>
                      {(course.progress || 0) >= 100 && (
                        <button
                          className="btn btn-success"
                          onClick={() => handleViewCourseCertificate(course.id)}
                        >
                          الشهادة
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="section recent-courses">
          <div className="section-header">
            <h2>دورات مقترحة</h2>
            <button
              className="btn btn-primary"
              onClick={() => navigate('/courses')}
            >
              عرض جميع الدورات
            </button>
          </div>

          {recentCourses.length === 0 ? (
            <div className="empty-state">
              <p>لا توجد دورات متاحة حالياً</p>
            </div>
          ) : (
            <div className="courses-list">
              {recentCourses.map(course => (
                <div key={course.id} className="course-item">
                  <div className="course-info">
                    <h3>{course.title}</h3>
                    <p>{course.description.substring(0, 100)}...</p>
                    <div className="course-meta">
                      <span className="instructor">{course.instructor}</span>
                      <span className="duration">{course.duration} يوم</span>
                      <span className="date">{formatDate(course.createdAt)}</span>
                    </div>
                  </div>
                  <div className="course-actions">
                    <button
                      className="btn btn-info"
                      onClick={() => navigate(`/courses/${course.id}`)}
                    >
                      عرض التفاصيل
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={() => handleEnrollCourse(course.id)}
                    >
                      التسجيل في الدورة
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
